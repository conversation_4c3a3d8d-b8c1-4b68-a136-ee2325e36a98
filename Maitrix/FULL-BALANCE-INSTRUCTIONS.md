# 🚀 Instruksi <PERSON> ke 100% Full Balance

## 📝 <PERSON><PERSON>an yang <PERSON>

### 1. Ganti Fungsi `getOptimalMintAmount`

**Cari di index.js sekitar baris 1136-1165:**
```javascript
async function getOptimalMintAmount(inputTokenAddress, minAmount, decimals) {
  // ... kode lama dengan 99.9% balance
}
```

**Ganti dengan:**
```javascript
async function getOptimalMintAmount(inputTokenAddress, minAmount, decimals) {
  try {
    const contract = new ethers.Contract(inputTokenAddress, ERC20ABI, globalWallet);
    const balance = await contract.balanceOf(globalWallet.address);
    const balanceFormatted = ethers.formatUnits(balance, decimals);
    
    // Convert to number for calculation
    const balanceNum = parseFloat(balanceFormatted);
    
    // Check if we have minimum required
    if (balanceNum < minAmount) {
      addLog(`⚠️ Balance ${balanceNum} < minimum ${minAmount} untuk mint`, "warning");
      return null;
    }
    
    // Use 100% of balance - FULL BALANCE!
    const fullAmount = balanceNum;
    
    addLog(`💰 MINT: Using FULL balance ${fullAmount} (100%)`, "system");
    return fullAmount;
    
  } catch (error) {
    addLog(`Error getting full balance for ${inputTokenAddress}: ${error.message}`, "error");
    return null;
  }
}
```

### 2. Ganti Fungsi `getOptimalStakeAmount`

**Cari di index.js sekitar baris 1167-1196:**
```javascript
async function getOptimalStakeAmount(tokenAddress, minAmount, decimals) {
  // ... kode lama dengan 99.8% balance
}
```

**Ganti dengan:**
```javascript
async function getOptimalStakeAmount(tokenAddress, minAmount, decimals) {
  try {
    const contract = new ethers.Contract(tokenAddress, ERC20ABI, globalWallet);
    const balance = await contract.balanceOf(globalWallet.address);
    const balanceFormatted = ethers.formatUnits(balance, decimals);
    
    // Convert to number for calculation
    const balanceNum = parseFloat(balanceFormatted);
    
    // Check if we have minimum required
    if (balanceNum < minAmount) {
      addLog(`⚠️ Balance ${balanceNum} < minimum ${minAmount} untuk stake`, "warning");
      return null;
    }
    
    // Use 100% of balance - FULL BALANCE!
    const fullAmount = balanceNum;
    
    addLog(`💰 STAKE: Using FULL balance ${fullAmount} (100%)`, "system");
    return fullAmount;
    
  } catch (error) {
    addLog(`Error getting full balance for ${tokenAddress}: ${error.message}`, "error");
    return null;
  }
}
```

### 3. Update Log Messages

**Cari dan ganti semua log yang menyebutkan "99.9%" atau "99.8%" dengan "100%"**

Contoh perubahan:
- `"Optimal Amount: 99.9%"` → `"Full Amount: 100%"`
- `"Buffer: 0.1%"` → `"No Buffer: 100%"`
- `"Optimal=${optimalAmount}"` → `"Full=${fullAmount}"`

## 🎯 Hasil Setelah Perubahan

### Sebelum (dengan buffer):
```
ATH Balance: 50.0000
Mint Amount: 49.9500 (99.9%)
Buffer: 0.0500
```

### Sesudah (100% full):
```
ATH Balance: 50.0000
Mint Amount: 50.0000 (100%)
Buffer: 0.0000
```

## ⚠️ Catatan Penting

1. **Risk**: Menggunakan 100% balance bisa menyebabkan transaksi gagal jika ada gas fee atau transfer fee
2. **Solution**: Pastikan wallet memiliki ETH yang cukup untuk gas fee
3. **Testing**: Test dengan amount kecil dulu sebelum full operation

## 🔧 Cara Apply Perubahan

1. Buka file `index.js`
2. Cari fungsi `getOptimalMintAmount` (sekitar baris 1136)
3. Replace dengan kode baru di atas
4. Cari fungsi `getOptimalStakeAmount` (sekitar baris 1167)
5. Replace dengan kode baru di atas
6. Save file
7. Restart bot dengan `npm start`

## ✅ Verifikasi

Setelah perubahan, cek log untuk memastikan:
- Muncul pesan "Using FULL balance (100%)"
- Tidak ada lagi pesan tentang "99.9%" atau "99.8%"
- Amount yang digunakan = exact balance yang dimiliki

## 🚀 Expected Result

Bot akan menggunakan 100% dari semua token yang dimiliki untuk mint dan stake operations, tanpa menyisakan buffer apapun.
