// Patch untuk mengubah logic menjadi 100% balance
// Ganti fungsi getOptimalMintAmount dan getOptimalStakeAmount

async function getFullBalanceAmount(tokenAddress, minAmount, decimals, operation = "mint") {
  try {
    const contract = new ethers.Contract(tokenAddress, ERC20ABI, globalWallet);
    const balance = await contract.balanceOf(globalWallet.address);
    const balanceFormatted = ethers.formatUnits(balance, decimals);
    
    // Convert to number for calculation
    const balanceNum = parseFloat(balanceFormatted);
    
    // Check if we have minimum required
    if (balanceNum < minAmount) {
      addLog(`⚠️ Balance ${balanceNum} < minimum ${minAmount} untuk ${operation}`, "warning");
      return null;
    }
    
    // Use 100% of balance - FULL BALANCE!
    const fullAmount = balanceNum;
    
    addLog(`💰 ${operation.toUpperCase()}: Using FULL balance ${fullAmount} (100%)`, "system");
    return fullAmount;
    
  } catch (error) {
    addLog(`Error getting full balance for ${tokenAddress}: ${error.message}`, "error");
    return null;
  }
}

// Alias untuk backward compatibility
async function getOptimalMintAmount(inputTokenAddress, minAmount, decimals) {
  return await getFullBalanceAmount(inputTokenAddress, minAmount, decimals, "mint");
}

async function getOptimalStakeAmount(tokenAddress, minAmount, decimals) {
  return await getFullBalanceAmount(tokenAddress, minAmount, decimals, "stake");
}

console.log("✅ Full Balance Patch Ready!");
console.log("📝 Instructions:");
console.log("1. Replace getOptimalMintAmount function in index.js with getFullBalanceAmount");
console.log("2. Replace getOptimalStakeAmount function in index.js with getFullBalanceAmount");
console.log("3. Update all calls to use 100% balance");
console.log("");
console.log("🎯 Result: Bot will use 100% of available balance for all operations");
