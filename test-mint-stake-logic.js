// Test script untuk memverifikasi logika mint dan stake dengan simulasi claim berhasil
import "dotenv/config";
import { ethers } from "ethers";

// Environment variables
const RPC_URL = process.env.RPC_URL;
const PRIVATE_KEY = process.env.PRIVATE_KEY;

// <PERSON><PERSON><PERSON>i claimed amounts (seolah-olah berhasil claim)
let claimedAmounts = {
  ATH: 50,       // Akan di-mint menjadi AUSD
  USDe: 1,       // Bisa mint OUSD atau langsung stake USDe
  LULUSD: 1,     // Langsung di-stake
  Ai16Z: 7,      // Akan di-mint menjadi AZUSD
  Virtual: 2,    // Akan di-mint menjadi VUSD
  Vana: 0.25,    // Akan di-mint menjadi VANAUSD
  USD1: 1,       // Langsung di-stake
  OG: 1          // Langsung di-stake
};

// Mapping token faucet ke token yang bisa di-mint
const FAUCET_TO_MINT_MAPPING = {
  ATH: "AUSD",
  Virtual: "VUSD", 
  Ai16Z: "AZUSD",
  Vana: "VANAUSD",
  USDe: "OUSD"
};

// Token yang langsung bisa di-stake tanpa mint
const DIRECT_STAKE_TOKENS = ["USDe", "USDE", "LULUSD", "USD1", "OG"];

// Mock ERC20 ABI (minimal)
const ERC20ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function decimals() view returns (uint8)"
];

let provider;
let globalWallet;

async function initializeTest() {
  try {
    console.log("🔧 Initializing test environment...");
    
    if (!RPC_URL || !PRIVATE_KEY) {
      throw new Error("RPC_URL atau PRIVATE_KEY tidak ditemukan di environment variables");
    }

    // Initialize provider
    provider = new ethers.JsonRpcProvider(RPC_URL);
    
    // Initialize wallet
    let privateKey = PRIVATE_KEY;
    if (!privateKey.startsWith('0x')) {
      privateKey = '0x' + privateKey;
    }
    globalWallet = new ethers.Wallet(privateKey, provider);
    
    console.log(`✅ Connected to wallet: ${globalWallet.address.slice(0, 6)}...${globalWallet.address.slice(-4)}`);
    
    return true;
  } catch (error) {
    console.error(`❌ Initialization failed: ${error.message}`);
    return false;
  }
}

// Simulasi fungsi getOptimalMintAmount yang sudah dimodifikasi
async function testGetOptimalMintAmount(inputTokenAddress, minAmount, decimals, token = null) {
  try {
    console.log(`\n🏭 Testing getOptimalMintAmount for ${token}`);
    
    // Simulasi balance check
    const mockBalance = ethers.parseUnits("100", decimals); // Simulasi balance 100 token
    console.log(`   Mock balance: 100 tokens`);

    if (mockBalance === 0n) {
      console.log(`   ❌ Balance is zero`);
      return null;
    }

    // Cari token faucet yang sesuai dengan input token
    let faucetToken = null;
    for (const [fToken, mintToken] of Object.entries(FAUCET_TO_MINT_MAPPING)) {
      if (mintToken === token) {
        faucetToken = fToken;
        break;
      }
    }

    // Jika ada jumlah claim yang tersimpan, gunakan jumlah tersebut
    if (faucetToken && claimedAmounts[faucetToken] > 0) {
      const claimedAmount = claimedAmounts[faucetToken];
      const claimedAmountWei = ethers.parseUnits(claimedAmount.toString(), decimals);
      
      // Pastikan balance cukup untuk jumlah yang di-claim
      if (mockBalance >= claimedAmountWei) {
        console.log(`   ✅ Menggunakan jumlah sesuai claim faucet ${faucetToken}: ${claimedAmount}`);
        return claimedAmount.toString();
      } else {
        console.log(`   ⚠️ Balance tidak cukup untuk jumlah claim (${claimedAmount}), menggunakan balance tersedia`);
      }
    }

    // Fallback ke logika lama jika tidak ada claim amount
    const amountToUse = mockBalance;
    const minAmountWei = ethers.parseUnits(minAmount.toString(), decimals);

    if (amountToUse < minAmountWei) {
      console.log(`   ❌ Balance tidak cukup untuk minimum amount`);
      return null;
    }

    const result = ethers.formatUnits(amountToUse, decimals);
    console.log(`   ✅ Fallback: menggunakan full balance: ${result}`);
    return result;
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return null;
  }
}

// Simulasi fungsi getOptimalStakeAmount yang sudah dimodifikasi
async function testGetOptimalStakeAmount(tokenAddress, minAmount, decimals, token = null) {
  try {
    console.log(`\n🥩 Testing getOptimalStakeAmount for ${token}`);
    
    // Simulasi balance check
    const mockBalance = ethers.parseUnits("100", decimals); // Simulasi balance 100 token
    console.log(`   Mock balance: 100 tokens`);

    if (mockBalance === 0n) {
      console.log(`   ❌ Balance is zero`);
      return null;
    }

    // Untuk token yang langsung bisa di-stake (tidak perlu mint)
    if (DIRECT_STAKE_TOKENS.includes(token)) {
      // Cari jumlah claim faucet yang sesuai
      let claimedAmount = 0;
      if (token === "USDe" || token === "USDE") claimedAmount = claimedAmounts["USDe"];
      else if (token === "LULUSD") claimedAmount = claimedAmounts["LULUSD"];
      else if (token === "USD1") claimedAmount = claimedAmounts["USD1"];
      else if (token === "OG") claimedAmount = claimedAmounts["OG"];

      if (claimedAmount > 0) {
        const claimedAmountWei = ethers.parseUnits(claimedAmount.toString(), decimals);
        
        // Pastikan balance cukup untuk jumlah yang di-claim
        if (mockBalance >= claimedAmountWei) {
          console.log(`   ✅ Menggunakan jumlah sesuai claim faucet untuk stake ${token}: ${claimedAmount}`);
          return claimedAmount.toString();
        } else {
          console.log(`   ⚠️ Balance tidak cukup untuk jumlah claim (${claimedAmount}), menggunakan balance tersedia`);
        }
      }
    }

    // Untuk token yang di-mint, gunakan 100% balance hasil mint
    // Fallback ke logika lama
    const amountToUse = mockBalance;
    const minAmountWei = ethers.parseUnits(minAmount.toString(), decimals);

    if (amountToUse < minAmountWei) {
      console.log(`   ❌ Balance tidak cukup untuk minimum amount`);
      return null;
    }

    const result = ethers.formatUnits(amountToUse, decimals);
    console.log(`   ✅ Menggunakan 100% balance: ${result}`);
    return result;
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return null;
  }
}

async function testMintLogic() {
  console.log("\n🏭 TESTING MINT LOGIC");
  console.log("=====================");
  
  const mintableTokens = ["AUSD", "VUSD", "VANAUSD", "AZUSD", "OUSD"];
  
  for (const token of mintableTokens) {
    const decimals = 18; // Simulasi decimals
    const minAmount = 0.1; // Simulasi min amount
    const inputTokenAddress = "******************************************"; // Mock address
    
    const result = await testGetOptimalMintAmount(inputTokenAddress, minAmount, decimals, token);
    
    if (result) {
      console.log(`   📊 ${token}: Will mint ${result} tokens`);
    } else {
      console.log(`   📊 ${token}: Cannot mint (insufficient balance or no claim data)`);
    }
  }
}

async function testStakeLogic() {
  console.log("\n🥩 TESTING STAKE LOGIC");
  console.log("======================");
  
  const stakableTokens = ["AZUSD", "AUSD", "VANAUSD", "VUSD", "USDE", "LULUSD", "OUSD", "USD1"];
  
  for (const token of stakableTokens) {
    const decimals = 18; // Simulasi decimals
    const minAmount = 0.0001; // Simulasi min amount
    const tokenAddress = "******************************************"; // Mock address
    
    const result = await testGetOptimalStakeAmount(tokenAddress, minAmount, decimals, token);
    
    if (result) {
      console.log(`   📊 ${token}: Will stake ${result} tokens`);
    } else {
      console.log(`   📊 ${token}: Cannot stake (insufficient balance)`);
    }
  }
}

function showExpectedFlow() {
  console.log("\n📋 EXPECTED FLOW BASED ON CLAIMED AMOUNTS");
  console.log("==========================================");
  
  console.log("\n🎯 Mint Operations (using claim amounts):");
  for (const [faucetToken, mintToken] of Object.entries(FAUCET_TO_MINT_MAPPING)) {
    const claimedAmount = claimedAmounts[faucetToken];
    console.log(`   ${faucetToken} (${claimedAmount}) → Mint ${mintToken} (${claimedAmount})`);
  }
  
  console.log("\n🎯 Stake Operations:");
  console.log("   Direct stake tokens (using claim amounts):");
  for (const token of DIRECT_STAKE_TOKENS) {
    const claimedAmount = claimedAmounts[token];
    console.log(`     ${token}: Stake ${claimedAmount} (from faucet claim)`);
  }
  
  console.log("   Minted tokens (using 100% balance from mint):");
  for (const mintToken of Object.values(FAUCET_TO_MINT_MAPPING)) {
    console.log(`     ${mintToken}: Stake 100% balance (from mint result)`);
  }
}

async function runTests() {
  console.log("🚀 Starting Mint & Stake Logic Tests");
  console.log("====================================");
  
  const initialized = await initializeTest();
  if (!initialized) {
    console.log("❌ Test initialization failed");
    return;
  }
  
  console.log("\n📊 Simulated Claimed Amounts:");
  for (const [token, amount] of Object.entries(claimedAmounts)) {
    console.log(`   ${token}: ${amount}`);
  }
  
  await testMintLogic();
  await testStakeLogic();
  showExpectedFlow();
  
  console.log("\n✅ All tests completed!");
  console.log("\n📝 Summary:");
  console.log("- ✅ Mint logic uses claimed amounts when available");
  console.log("- ✅ Stake logic uses claimed amounts for direct stake tokens");
  console.log("- ✅ Stake logic uses 100% balance for minted tokens");
  console.log("- ✅ Fallback to full balance when no claim data available");
}

// Run the tests
runTests().catch(console.error);
