// Test script untuk memverifikasi semua fungsi bot
import "dotenv/config";
import { ethers } from "ethers";

// Test configuration
const TEST_MODE = true;
const QUICK_TEST = true; // Set false untuk test 24 jam penuh

// Environment variables
const RPC_URL = process.env.RPC_URL;
const PRIVATE_KEY = process.env.PRIVATE_KEY;
const PRIVATE_KEY_2 = process.env.PRIVATE_KEY_2;

// Test results tracking
let testResults = {
  walletInit: false,
  balanceCheck: false,
  claimFaucet: false,
  mintTokens: false,
  stakeTokens: false,
  walletSwitch: false,
  fullCycle: false,
  looping: false
};

let walletList = [];
let currentWalletIndex = 0;
let provider = null;
let globalWallet = null;

// Test functions
async function testWalletInitialization() {
  console.log("🔧 Testing Wallet Initialization...");
  
  try {
    // Initialize provider
    provider = new ethers.JsonRpcProvider(RPC_URL);
    await provider.getNetwork();
    console.log("✅ RPC Connection: SUCCESS");
    
    // Initialize wallets
    if (PRIVATE_KEY) {
      let pk1 = PRIVATE_KEY.startsWith('0x') ? PRIVATE_KEY : '0x' + PRIVATE_KEY;
      walletList.push({
        privateKey: pk1,
        name: "Wallet 1",
        wallet: new ethers.Wallet(pk1, provider)
      });
    }
    
    if (PRIVATE_KEY_2) {
      let pk2 = PRIVATE_KEY_2.startsWith('0x') ? PRIVATE_KEY_2 : '0x' + PRIVATE_KEY_2;
      walletList.push({
        privateKey: pk2,
        name: "Wallet 2", 
        wallet: new ethers.Wallet(pk2, provider)
      });
    }
    
    console.log(`✅ Wallets Initialized: ${walletList.length} wallets`);
    walletList.forEach((w, i) => {
      console.log(`   ${w.name}: ${w.wallet.address}`);
    });
    
    testResults.walletInit = true;
    return true;
    
  } catch (error) {
    console.log(`❌ Wallet Initialization: FAILED - ${error.message}`);
    return false;
  }
}

async function testBalanceCheck(walletData) {
  console.log(`💰 Testing Balance Check for ${walletData.name}...`);
  
  try {
    const wallet = walletData.wallet;
    
    // Test ETH balance
    const ethBalance = await provider.getBalance(wallet.address);
    const ethFormatted = ethers.formatEther(ethBalance);
    console.log(`   ETH: ${ethFormatted}`);
    
    // Test token balances (sample)
    const tokenAddresses = {
      "ATH": process.env.ATH_ADDRESS,
      "AI16Z": process.env.AI16Z_ADDRESS,
      "USDE": process.env.USDE_ADDRESS
    };
    
    for (const [symbol, address] of Object.entries(tokenAddresses)) {
      if (address) {
        try {
          const contract = new ethers.Contract(address, [
            "function balanceOf(address) view returns (uint256)",
            "function decimals() view returns (uint8)"
          ], wallet);
          
          const balance = await contract.balanceOf(wallet.address);
          const decimals = await contract.decimals();
          const formatted = ethers.formatUnits(balance, decimals);
          console.log(`   ${symbol}: ${formatted}`);
        } catch (e) {
          console.log(`   ${symbol}: Error - ${e.message}`);
        }
      }
    }
    
    testResults.balanceCheck = true;
    return true;
    
  } catch (error) {
    console.log(`❌ Balance Check: FAILED - ${error.message}`);
    return false;
  }
}

async function testFullBalanceLogic() {
  console.log("🧮 Testing Full Balance Logic (100%)...");
  
  try {
    // Simulate balance calculation
    const testBalance = 50.0000;
    const minAmount = 1.0;
    
    // Test 100% logic
    const fullAmount = testBalance; // 100% without buffer
    
    console.log(`   Test Balance: ${testBalance}`);
    console.log(`   Min Amount: ${minAmount}`);
    console.log(`   Full Amount (100%): ${fullAmount}`);
    console.log(`   Buffer: 0.0000 (No buffer - 100% usage)`);
    
    if (fullAmount === testBalance) {
      console.log("✅ Full Balance Logic: SUCCESS (100% usage confirmed)");
      return true;
    } else {
      console.log("❌ Full Balance Logic: FAILED (Not using 100%)");
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Full Balance Logic: FAILED - ${error.message}`);
    return false;
  }
}

async function testWalletSwitching() {
  console.log("🔄 Testing Wallet Switching...");
  
  try {
    if (walletList.length < 2) {
      console.log("⚠️ Only 1 wallet available, skipping switch test");
      return true;
    }
    
    // Test switching between wallets
    for (let i = 0; i < walletList.length; i++) {
      currentWalletIndex = i;
      globalWallet = walletList[i].wallet;
      console.log(`   Switched to ${walletList[i].name}: ${globalWallet.address}`);
    }
    
    console.log("✅ Wallet Switching: SUCCESS");
    testResults.walletSwitch = true;
    return true;
    
  } catch (error) {
    console.log(`❌ Wallet Switching: FAILED - ${error.message}`);
    return false;
  }
}

async function simulateFullCycle(walletData) {
  console.log(`🔄 Simulating Full Cycle for ${walletData.name}...`);
  
  try {
    // Step 1: Claim Faucets (simulation)
    console.log("   📥 Simulating Claim All Faucets...");
    const faucetTokens = ["ATH", "USDe", "LULUSD", "Ai16Z", "Virtual", "Vana", "USD1", "OG"];
    for (const token of faucetTokens) {
      console.log(`      Claiming ${token}... ✅`);
      await new Promise(resolve => setTimeout(resolve, 100)); // Quick simulation
    }
    
    // Step 2: Mint Tokens (simulation)
    console.log("   🏭 Simulating Mint All Tokens (100% balance)...");
    const mintableTokens = ["AUSD", "VUSD", "VANAUSD", "AZUSD", "OUSD"];
    for (const token of mintableTokens) {
      console.log(`      Minting ${token} with 100% balance... ✅`);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Step 3: Stake Tokens (simulation)
    console.log("   🥩 Simulating Stake All Tokens (100% balance)...");
    const stakableTokens = ["AZUSD", "AUSD", "VANAUSD", "VUSD", "USDE", "LULUSD", "OUSD", "USD1"];
    for (const token of stakableTokens) {
      console.log(`      Staking ${token} with 100% balance... ✅`);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log(`✅ Full Cycle for ${walletData.name}: SUCCESS`);
    return true;
    
  } catch (error) {
    console.log(`❌ Full Cycle for ${walletData.name}: FAILED - ${error.message}`);
    return false;
  }
}

async function testLooping24H() {
  console.log("⏰ Testing 24H Looping Logic...");
  
  try {
    const startTime = Date.now();
    const testDuration = QUICK_TEST ? 10000 : 24 * 60 * 60 * 1000; // 10 seconds or 24 hours
    let cycleCount = 0;
    
    console.log(`   Test Duration: ${QUICK_TEST ? '10 seconds' : '24 hours'}`);
    
    while ((Date.now() - startTime) < testDuration) {
      cycleCount++;
      const timeElapsed = Math.floor((Date.now() - startTime) / 1000);
      const timeRemaining = Math.floor((testDuration - (Date.now() - startTime)) / 1000);
      
      console.log(`   🔄 Cycle #${cycleCount} | Elapsed: ${timeElapsed}s | Remaining: ${timeRemaining}s`);
      
      // Simulate cycle for all wallets
      for (let i = 0; i < walletList.length; i++) {
        const walletData = walletList[i];
        console.log(`      Processing ${walletData.name}...`);
        await simulateFullCycle(walletData);
        
        if (i < walletList.length - 1) {
          console.log(`      Waiting before next wallet...`);
          await new Promise(resolve => setTimeout(resolve, QUICK_TEST ? 500 : 10000));
        }
      }
      
      // Wait between cycles
      if ((Date.now() - startTime) < testDuration) {
        console.log(`   ⏳ Waiting before next cycle...`);
        await new Promise(resolve => setTimeout(resolve, QUICK_TEST ? 1000 : 300000)); // 1s or 5min
      }
    }
    
    console.log(`✅ 24H Looping: SUCCESS (${cycleCount} cycles completed)`);
    testResults.looping = true;
    return true;
    
  } catch (error) {
    console.log(`❌ 24H Looping: FAILED - ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log("🚀 STARTING COMPREHENSIVE BOT TEST");
  console.log("=====================================");
  console.log("");
  
  // Test 1: Wallet Initialization
  await testWalletInitialization();
  console.log("");
  
  // Test 2: Balance Checking
  if (testResults.walletInit && walletList.length > 0) {
    for (const walletData of walletList) {
      await testBalanceCheck(walletData);
    }
  }
  console.log("");
  
  // Test 3: Full Balance Logic
  await testFullBalanceLogic();
  console.log("");
  
  // Test 4: Wallet Switching
  await testWalletSwitching();
  console.log("");
  
  // Test 5: Full Cycle Simulation
  if (testResults.walletInit) {
    testResults.fullCycle = true;
    for (const walletData of walletList) {
      const success = await simulateFullCycle(walletData);
      if (!success) testResults.fullCycle = false;
    }
  }
  console.log("");
  
  // Test 6: 24H Looping
  await testLooping24H();
  console.log("");
  
  // Final Results
  console.log("📊 TEST RESULTS SUMMARY");
  console.log("========================");
  console.log(`✅ Wallet Initialization: ${testResults.walletInit ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Balance Check: ${testResults.balanceCheck ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Claim Faucet: ${testResults.claimFaucet ? 'PASS' : 'SIMULATED'}`);
  console.log(`✅ Mint Tokens (100%): ${testResults.mintTokens ? 'PASS' : 'SIMULATED'}`);
  console.log(`✅ Stake Tokens (100%): ${testResults.stakeTokens ? 'PASS' : 'SIMULATED'}`);
  console.log(`✅ Wallet Switching: ${testResults.walletSwitch ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Full Cycle: ${testResults.fullCycle ? 'PASS' : 'FAIL'}`);
  console.log(`✅ 24H Looping: ${testResults.looping ? 'PASS' : 'FAIL'}`);
  console.log("");
  
  const allPassed = Object.values(testResults).every(result => result === true);
  console.log(`🎯 OVERALL RESULT: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log("");
    console.log("🚀 BOT IS READY FOR 24H OPERATION!");
    console.log("   - All wallets initialized successfully");
    console.log("   - Balance checking works correctly");
    console.log("   - Full balance logic (100%) confirmed");
    console.log("   - Wallet switching functional");
    console.log("   - Full cycle simulation successful");
    console.log("   - 24H looping logic verified");
    console.log("");
    console.log("💡 To start actual bot: npm start");
  }
}

// Run tests
runAllTests().catch(console.error);
