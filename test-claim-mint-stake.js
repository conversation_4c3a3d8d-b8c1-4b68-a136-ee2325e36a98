// Test script untuk memverifikasi logika claim-mint-stake yang baru
import "dotenv/config";
import { ethers } from "ethers";

// Test configuration
const TEST_MODE = true;

// Environment variables
const RPC_URL = process.env.RPC_URL;
const PRIVATE_KEY = process.env.PRIVATE_KEY;

// Mock data untuk testing
let claimedAmounts = {
  ATH: 100,      // Akan di-mint menjadi AUSD
  USDe: 50,      // Langsung di-stake
  LULUSD: 75,    // Langsung di-stake
  Ai16Z: 25,     // Akan di-mint menjadi AZUSD
  Virtual: 30,   // Akan di-mint menjadi VUSD
  Vana: 10,      // Akan di-mint menjadi VANAUSD
  USD1: 40,      // Langsung di-stake
  OG: 20         // Langsung di-stake
};

// Mapping token faucet ke token yang bisa di-mint
const FAUCET_TO_MINT_MAPPING = {
  ATH: "AUSD",
  Virtual: "VUSD", 
  Ai16Z: "AZUSD",
  Vana: "VANAUSD",
  USDe: "OUSD"
};

// Token yang langsung bisa di-stake tanpa mint
const DIRECT_STAKE_TOKENS = ["USDe", "LULUSD", "USD1", "OG"];

function testClaimAmountLogic() {
  console.log("🧪 Testing Claim Amount Logic");
  console.log("================================");
  
  // Test 1: Token yang perlu di-mint
  console.log("\n📋 Test 1: Token yang perlu di-mint");
  for (const [faucetToken, mintToken] of Object.entries(FAUCET_TO_MINT_MAPPING)) {
    const claimedAmount = claimedAmounts[faucetToken];
    console.log(`   ${faucetToken} (${claimedAmount}) → Mint ${mintToken} (${claimedAmount}) → Stake ${mintToken} (${claimedAmount})`);
  }
  
  // Test 2: Token yang langsung di-stake
  console.log("\n📋 Test 2: Token yang langsung di-stake");
  for (const token of DIRECT_STAKE_TOKENS) {
    const claimedAmount = claimedAmounts[token];
    console.log(`   ${token} (${claimedAmount}) → Stake ${token} (${claimedAmount})`);
  }
}

function testMintAmountCalculation(token) {
  console.log(`\n🏭 Testing Mint Amount for ${token}`);
  
  // Cari token faucet yang sesuai
  let faucetToken = null;
  for (const [fToken, mintToken] of Object.entries(FAUCET_TO_MINT_MAPPING)) {
    if (mintToken === token) {
      faucetToken = fToken;
      break;
    }
  }
  
  if (faucetToken && claimedAmounts[faucetToken] > 0) {
    const claimedAmount = claimedAmounts[faucetToken];
    console.log(`   ✅ Menggunakan jumlah sesuai claim faucet ${faucetToken}: ${claimedAmount}`);
    return claimedAmount;
  } else {
    console.log(`   ⚠️ Tidak ada claim amount untuk ${token}, menggunakan balance tersedia`);
    return null;
  }
}

function testStakeAmountCalculation(token) {
  console.log(`\n🥩 Testing Stake Amount for ${token}`);
  
  // Untuk token yang langsung bisa di-stake
  if (DIRECT_STAKE_TOKENS.includes(token)) {
    const claimedAmount = claimedAmounts[token];
    if (claimedAmount > 0) {
      console.log(`   ✅ Menggunakan jumlah sesuai claim faucet untuk stake ${token}: ${claimedAmount}`);
      return claimedAmount;
    } else {
      console.log(`   ⚠️ Tidak ada claim amount untuk ${token}, menggunakan balance tersedia`);
      return null;
    }
  } else {
    console.log(`   ✅ Token ${token} hasil mint, menggunakan 100% balance hasil mint`);
    return "100% balance";
  }
}

function testFullWorkflow() {
  console.log("\n🔄 Testing Full Workflow");
  console.log("=========================");
  
  console.log("\n📥 Step 1: Claim All Faucets");
  for (const [token, amount] of Object.entries(claimedAmounts)) {
    console.log(`   Claim ${token}: ${amount} tokens`);
  }
  
  console.log("\n🏭 Step 2: Mint Tokens (berdasarkan claim amount)");
  const mintableTokens = ["AUSD", "VUSD", "VANAUSD", "AZUSD", "OUSD"];
  for (const token of mintableTokens) {
    const mintAmount = testMintAmountCalculation(token);
    if (mintAmount) {
      console.log(`   Mint ${token}: ${mintAmount} tokens`);
    }
  }
  
  console.log("\n🥩 Step 3: Stake Tokens");
  const stakableTokens = ["AZUSD", "AUSD", "VANAUSD", "VUSD", "USDE", "LULUSD", "OUSD", "USD1"];
  for (const token of stakableTokens) {
    const stakeAmount = testStakeAmountCalculation(token);
    if (stakeAmount) {
      console.log(`   Stake ${token}: ${stakeAmount} tokens`);
    }
  }
}

function testExpectedResults() {
  console.log("\n📊 Expected Results Summary");
  console.log("============================");
  
  console.log("\n🎯 Mint Operations (menggunakan jumlah claim faucet):");
  console.log(`   ATH (100) → Mint AUSD (100)`);
  console.log(`   Virtual (30) → Mint VUSD (30)`);
  console.log(`   Ai16Z (25) → Mint AZUSD (25)`);
  console.log(`   Vana (10) → Mint VANAUSD (10)`);
  console.log(`   USDe (50) → Mint OUSD (50)`);
  
  console.log("\n🎯 Stake Operations:");
  console.log(`   AUSD: Stake 100 (dari hasil mint)`);
  console.log(`   VUSD: Stake 30 (dari hasil mint)`);
  console.log(`   AZUSD: Stake 25 (dari hasil mint)`);
  console.log(`   VANAUSD: Stake 10 (dari hasil mint)`);
  console.log(`   OUSD: Stake 50 (dari hasil mint)`);
  console.log(`   USDe: Stake 50 (langsung dari claim faucet)`);
  console.log(`   LULUSD: Stake 75 (langsung dari claim faucet)`);
  console.log(`   USD1: Stake 40 (langsung dari claim faucet)`);
  console.log(`   OG: Stake 20 (langsung dari claim faucet)`);
}

// Run tests
console.log("🚀 Starting Claim-Mint-Stake Logic Tests");
console.log("=========================================");

testClaimAmountLogic();
testFullWorkflow();
testExpectedResults();

console.log("\n✅ All tests completed!");
console.log("\n📝 Key Changes Made:");
console.log("1. ✅ Modified claimFaucet() to store claimed amounts");
console.log("2. ✅ Updated getOptimalMintAmount() to use claimed amounts");
console.log("3. ✅ Updated getOptimalStakeAmount() to use claimed amounts for direct stake tokens");
console.log("4. ✅ Added resetClaimedAmounts() and showClaimedAmounts() helper functions");
console.log("5. ✅ Updated all function calls to pass token parameter");
console.log("6. ✅ Fixed ethers.js v6 syntax compatibility");

console.log("\n🎯 Expected Behavior:");
console.log("- Token yang perlu di-mint: Gunakan jumlah claim faucet untuk mint, lalu stake hasil mint");
console.log("- Token langsung stake: Gunakan jumlah claim faucet untuk stake langsung");
console.log("- Konsistensi jumlah antara claim → mint → stake");
