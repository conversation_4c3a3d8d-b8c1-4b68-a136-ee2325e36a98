// Test script untuk simulasi skenario nyata dengan data yang sudah ada di wallet
import "dotenv/config";
import { ethers } from "ethers";

// Environment variables
const RPC_URL = process.env.RPC_URL;
const PRIVATE_KEY = process.env.PRIVATE_KEY;

// Token addresses dari environment
const ATH_ADDRESS = process.env.ATH_ADDRESS;
const AI16Z_ADDRESS = process.env.AI16Z_ADDRESS;
const USDE_ADDRESS = process.env.USDE_ADDRESS;
const VANA_ADDRESS = process.env.VANA_ADDRESS;
const VIRTUAL_ADDRESS = process.env.VIRTUAL_ADDRESS;
const LULUSD_ADDRESS = process.env.LULUSD_ADDRESS;

// ERC20 ABI
const ERC20ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function decimals() view returns (uint8)",
  "function symbol() view returns (string)"
];

let provider;
let globalWallet;

// Simulasi claimed amounts berdasarkan balance yang ada
let claimedAmounts = {
  ATH: 0,
  USDe: 0,
  LULUSD: 0,
  Ai16Z: 0,
  Virtual: 0,
  Vana: 0,
  USD1: 0,
  OG: 0
};

// Mapping token faucet ke token yang bisa di-mint
const FAUCET_TO_MINT_MAPPING = {
  ATH: "AUSD",
  Virtual: "VUSD", 
  Ai16Z: "AZUSD",
  Vana: "VANAUSD",
  USDe: "OUSD"
};

// Token yang langsung bisa di-stake tanpa mint
const DIRECT_STAKE_TOKENS = ["USDe", "USDE", "LULUSD", "USD1", "OG"];

async function initializeTest() {
  try {
    console.log("🔧 Initializing test environment...");
    
    if (!RPC_URL || !PRIVATE_KEY) {
      throw new Error("RPC_URL atau PRIVATE_KEY tidak ditemukan di environment variables");
    }

    // Initialize provider
    provider = new ethers.JsonRpcProvider(RPC_URL);
    
    // Initialize wallet
    let privateKey = PRIVATE_KEY;
    if (!privateKey.startsWith('0x')) {
      privateKey = '0x' + privateKey;
    }
    globalWallet = new ethers.Wallet(privateKey, provider);
    
    console.log(`✅ Connected to wallet: ${globalWallet.address.slice(0, 6)}...${globalWallet.address.slice(-4)}`);
    
    return true;
  } catch (error) {
    console.error(`❌ Initialization failed: ${error.message}`);
    return false;
  }
}

async function checkTokenBalance(tokenAddress, tokenName) {
  try {
    if (!tokenAddress || tokenAddress === "undefined") {
      console.log(`   ⚠️ ${tokenName}: Address not found in environment`);
      return 0;
    }

    const contract = new ethers.Contract(tokenAddress, ERC20ABI, globalWallet);
    const balance = await contract.balanceOf(globalWallet.address);
    const decimals = await contract.decimals();
    const balanceFormatted = ethers.formatUnits(balance, decimals);
    
    console.log(`   ${tokenName}: ${balanceFormatted} tokens`);
    return parseFloat(balanceFormatted);
  } catch (error) {
    console.log(`   ❌ ${tokenName}: Error checking balance - ${error.message}`);
    return 0;
  }
}

async function simulateClaimedAmounts() {
  console.log("\n📊 Checking current token balances (simulating claimed amounts):");
  
  // Simulasi: gunakan sebagian dari balance yang ada sebagai "claimed amount"
  const athBalance = await checkTokenBalance(ATH_ADDRESS, "ATH");
  const ai16zBalance = await checkTokenBalance(AI16Z_ADDRESS, "Ai16Z");
  const usdeBalance = await checkTokenBalance(USDE_ADDRESS, "USDe");
  const vanaBalance = await checkTokenBalance(VANA_ADDRESS, "Vana");
  const virtualBalance = await checkTokenBalance(VIRTUAL_ADDRESS, "Virtual");
  const lulusdBalance = await checkTokenBalance(LULUSD_ADDRESS, "LULUSD");
  
  // Simulasi claimed amounts (gunakan 50% dari balance yang ada)
  claimedAmounts = {
    ATH: Math.min(athBalance * 0.5, 50), // Max 50 sesuai faucet limit
    USDe: Math.min(usdeBalance * 0.5, 1), // Max 1 sesuai faucet limit
    LULUSD: Math.min(lulusdBalance * 0.5, 1), // Max 1 sesuai faucet limit
    Ai16Z: Math.min(ai16zBalance * 0.5, 7), // Max 7 sesuai faucet limit
    Virtual: Math.min(virtualBalance * 0.5, 2), // Max 2 sesuai faucet limit
    Vana: Math.min(vanaBalance * 0.5, 0.25), // Max 0.25 sesuai faucet limit
    USD1: 1, // Simulasi
    OG: 1    // Simulasi
  };
  
  console.log("\n🎯 Simulated claimed amounts (50% of current balance, capped by faucet limits):");
  for (const [token, amount] of Object.entries(claimedAmounts)) {
    if (amount > 0) {
      console.log(`   ${token}: ${amount}`);
    }
  }
}

async function testMintLogicWithRealData() {
  console.log("\n🏭 TESTING MINT LOGIC WITH REAL DATA");
  console.log("====================================");
  
  const mintableTokens = ["AUSD", "VUSD", "VANAUSD", "AZUSD", "OUSD"];
  
  for (const token of mintableTokens) {
    console.log(`\n🏭 Testing mint for ${token}:`);
    
    // Cari token faucet yang sesuai
    let faucetToken = null;
    for (const [fToken, mintToken] of Object.entries(FAUCET_TO_MINT_MAPPING)) {
      if (mintToken === token) {
        faucetToken = fToken;
        break;
      }
    }
    
    if (faucetToken && claimedAmounts[faucetToken] > 0) {
      const claimedAmount = claimedAmounts[faucetToken];
      console.log(`   ✅ Will mint ${claimedAmount} ${token} (using ${faucetToken} claim amount)`);
    } else {
      console.log(`   ⚠️ No claimed amount for ${token}, would use full balance`);
    }
  }
}

async function testStakeLogicWithRealData() {
  console.log("\n🥩 TESTING STAKE LOGIC WITH REAL DATA");
  console.log("=====================================");
  
  const stakableTokens = ["AZUSD", "AUSD", "VANAUSD", "VUSD", "USDE", "LULUSD", "OUSD", "USD1"];
  
  for (const token of stakableTokens) {
    console.log(`\n🥩 Testing stake for ${token}:`);
    
    // Untuk token yang langsung bisa di-stake
    if (DIRECT_STAKE_TOKENS.includes(token)) {
      let claimedAmount = 0;
      if (token === "USDe" || token === "USDE") claimedAmount = claimedAmounts["USDe"];
      else if (token === "LULUSD") claimedAmount = claimedAmounts["LULUSD"];
      else if (token === "USD1") claimedAmount = claimedAmounts["USD1"];
      else if (token === "OG") claimedAmount = claimedAmounts["OG"];

      if (claimedAmount > 0) {
        console.log(`   ✅ Will stake ${claimedAmount} ${token} (using faucet claim amount)`);
      } else {
        console.log(`   ⚠️ No claimed amount for ${token}, would use full balance`);
      }
    } else {
      console.log(`   ✅ Will stake 100% balance of ${token} (from mint result)`);
    }
  }
}

function showExpectedWorkflow() {
  console.log("\n📋 EXPECTED WORKFLOW SUMMARY");
  console.log("============================");
  
  console.log("\n🔄 Step 1: Claim Faucets");
  for (const [token, amount] of Object.entries(claimedAmounts)) {
    if (amount > 0) {
      console.log(`   Claim ${token}: ${amount} tokens`);
    }
  }
  
  console.log("\n🔄 Step 2: Mint Tokens");
  for (const [faucetToken, mintToken] of Object.entries(FAUCET_TO_MINT_MAPPING)) {
    const claimedAmount = claimedAmounts[faucetToken];
    if (claimedAmount > 0) {
      console.log(`   Mint ${mintToken}: ${claimedAmount} tokens (from ${faucetToken} claim)`);
    }
  }
  
  console.log("\n🔄 Step 3: Stake Tokens");
  
  console.log("   Direct stake (using claim amounts):");
  for (const token of DIRECT_STAKE_TOKENS) {
    let claimedAmount = 0;
    if (token === "USDe" || token === "USDE") claimedAmount = claimedAmounts["USDe"];
    else if (token === "LULUSD") claimedAmount = claimedAmounts["LULUSD"];
    else if (token === "USD1") claimedAmount = claimedAmounts["USD1"];
    else if (token === "OG") claimedAmount = claimedAmounts["OG"];
    
    if (claimedAmount > 0) {
      console.log(`     Stake ${token}: ${claimedAmount} tokens`);
    }
  }
  
  console.log("   Stake minted tokens (using 100% mint result):");
  for (const mintToken of Object.values(FAUCET_TO_MINT_MAPPING)) {
    console.log(`     Stake ${mintToken}: 100% of minted amount`);
  }
}

async function runRealScenarioTest() {
  console.log("🚀 Starting Real Scenario Test");
  console.log("==============================");
  
  const initialized = await initializeTest();
  if (!initialized) {
    console.log("❌ Test initialization failed");
    return;
  }
  
  await simulateClaimedAmounts();
  await testMintLogicWithRealData();
  await testStakeLogicWithRealData();
  showExpectedWorkflow();
  
  console.log("\n✅ Real scenario test completed!");
  console.log("\n📝 Key Points:");
  console.log("- ✅ Mint amounts based on actual faucet claim amounts");
  console.log("- ✅ Stake amounts consistent with claim/mint amounts");
  console.log("- ✅ Direct stake tokens use faucet claim amounts");
  console.log("- ✅ Minted tokens use 100% of mint result for staking");
}

// Run the test
runRealScenarioTest().catch(console.error);
